using System;
using DG.Tweening;
// using TMPro;
using UnityEngine;

public class Slot : MonoBehaviour
{
    public static Vector3 OffsetPos = new Vector3(0, 0.3f, 0);

    public Renderer slotRenderer;
    public TextMesh txtNum;

    public bool IsEmpty { get { return _num == 0; } }
    public bool WithoutMajiang { get { return _maJiang == null; } }
    private MaJiang _maJiang;
    private int _num;
    internal bool IsMoving;
    [NonSerialized] public bool IsLock;
    [NonSerialized] public bool IsOpenByShare;
    public bool UnReadyMerge { get; set; }
    private Material material;
    private Color initColor;
    private Vector3 initPos;
    private void Awake()
    {
        material = slotRenderer.material;
        initColor = material.color;
        initPos = transform.position;
    }

    public void MoveBar(Vector3 pos)
    {
        initPos = transform.position = pos;
    }
    public void UpdateInitPos()
    {
        initPos = transform.position;
    }

    internal void Add(MaJiang maJiang)
    {
        _maJiang = maJiang;
        Num = maJiang.Num;
    }

    internal void MoveMajiang2Table()
    {
        if (_maJiang == null)
            return;

        var areaPos = BattleScene.Inst.bornArea.transform.position;
        var targetPos = BattleScene.Inst.bornArea.GetRandonPos();
        targetPos.y = areaPos.y;
        _maJiang.FlyToTable(targetPos);
        ClearMajiang();
    }

    internal void PunchMove(float punch, float duration, int vibrato = 10, float elasticity = 1f)
    {
        transform.DOKill(true);

        // 直接设置颜色变化，不做位置动画
        material.color = Color.white * 0.9f;
        material.color = initColor;
    }

    internal MaJiang GetMajiang()
    {
        return _maJiang;
    }
    internal void ClearMajiang()
    {
        _maJiang = null;
        Num = 0;
    }

    internal Vector3 GetMajiangPos()
    {
        return initPos;
    }

    internal int Num
    {
        get
        {
            return _num;
        }
        private set
        {
            _num = value;
            if (txtNum != null)
            {
                txtNum.text = value.ToString();
            }
        }
    }

    public bool IsMerging { get; internal set; }
}